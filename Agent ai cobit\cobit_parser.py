import os
import re
import json
from typing import Dict, List, Tuple, Any
from pathlib import Path

class COBITParser:
    """Parser pour les fichiers COBIT-2019-Design-Toolkit."""
    
    def __init__(self, resources_path: str = "../cobtiteresources"):
        self.resources_path = Path(resources_path)
        self.df_data = {}
        self.df_maps = {}
        self.load_all_data()
    
    def load_all_data(self):
        """Charge toutes les données COBIT pour tous les DFs."""
        for df_num in range(1, 11):
            self.load_df_data(df_num)
            self.load_df_map(df_num)
    
    def load_df_data(self, df_num: int):
        """Charge les données d'un DF spécifique (scores, baselines, relative importance)."""
        df_file = self.resources_path / f"COBIT-2019-Design-Toolkit__{df_num+2:02d}_DF{df_num}.txt"
        
        if not df_file.exists():
            print(f"⚠️ Fichier DF{df_num} non trouvé: {df_file}")
            return
        
        try:
            with open(df_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extraire les scores, baselines et relative importance
            scores_data = {}
            
            # Pattern pour extraire les données des objectifs COBIT
            pattern = r'([A-Z]{3}\d{2})\s+([\d.]+)\s+([\d.-]+)\s+([\d.-]+)'
            matches = re.findall(pattern, content)
            
            for match in matches:
                objective = match[0]
                score = float(match[1])
                baseline = float(match[2])
                relative_importance = float(match[3])
                
                scores_data[objective] = {
                    'score': score,
                    'baseline': baseline,
                    'relative_importance': relative_importance
                }
            
            self.df_data[f'DF{df_num}'] = scores_data
            print(f"✅ DF{df_num} chargé: {len(scores_data)} objectifs")
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement de DF{df_num}: {e}")
    
    def load_df_map(self, df_num: int):
        """Charge la matrice de pondération d'un DF spécifique."""
        df_map_file = self.resources_path / f"COBIT-2019-Design-Toolkit__{df_num+3:02d}_DF{df_num}map.txt"
        
        if not df_map_file.exists():
            print(f"⚠️ Fichier DF{df_num}map non trouvé: {df_map_file}")
            return
        
        try:
            with open(df_map_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Extraire les en-têtes (axes du DF)
            header_line = lines[0].strip()
            axes = header_line.split('\t')[1:]  # Ignorer la première colonne (DFx)
            
            # Extraire les pondérations pour chaque objectif
            map_data = {}
            
            for line in lines[1:]:
                parts = line.strip().split('\t')
                if len(parts) > 1:
                    objective = parts[0]
                    weights = [float(w) for w in parts[1:] if w.strip()]
                    
                    if len(weights) == len(axes):
                        map_data[objective] = {
                            'axes': axes,
                            'weights': weights
                        }
            
            self.df_maps[f'DF{df_num}'] = map_data
            print(f"✅ DF{df_num}map chargé: {len(map_data)} objectifs, {len(axes)} axes")
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement de DF{df_num}map: {e}")
    
    def get_df_scores(self, df_num: int) -> Dict[str, Any]:
        """Retourne les scores, baselines et relative importance pour un DF."""
        df_key = f'DF{df_num}'
        if df_key not in self.df_data:
            return {}
        
        return self.df_data[df_key]
    
    def get_df_map(self, df_num: int) -> Dict[str, Any]:
        """Retourne la matrice de pondération pour un DF."""
        df_key = f'DF{df_num}'
        if df_key not in self.df_maps:
            return {}
        
        return self.df_maps[df_key]
    
    def calculate_scores_with_inputs(self, df_num: int, inputs: List[float]) -> Dict[str, float]:
        """Calcule les scores pour un DF avec des inputs donnés."""
        df_map = self.get_df_map(df_num)
        if not df_map:
            return {}
        
        scores = {}
        
        for objective, data in df_map.items():
            weights = data['weights']
            if len(weights) == len(inputs):
                # Calcul du score pondéré
                score = sum(input_val * weight for input_val, weight in zip(inputs, weights))
                scores[objective] = score
        
        return scores
    
    def get_all_df_data(self) -> Dict[str, Any]:
        """Retourne toutes les données pour tous les DFs."""
        return {
            'df_scores': self.df_data,
            'df_maps': self.df_maps
        }
    
    def get_df_summary(self, df_num: int) -> Dict[str, Any]:
        """Retourne un résumé complet pour un DF."""
        scores = self.get_df_scores(df_num)
        df_map = self.get_df_map(df_num)
        
        if not scores:
            return {}
        
        # Calculer les moyennes et statistiques
        score_values = [data['score'] for data in scores.values()]
        baseline_values = [data['baseline'] for data in scores.values()]
        ri_values = [data['relative_importance'] for data in scores.values()]
        
        summary = {
            'df_number': df_num,
            'objectives_count': len(scores),
            'average_score': sum(score_values) / len(score_values) if score_values else 0,
            'average_baseline': sum(baseline_values) / len(baseline_values) if baseline_values else 0,
            'average_ri': sum(ri_values) / len(ri_values) if ri_values else 0,
            'scores': scores,
            'map_axes': list(df_map.values())[0]['axes'] if df_map else []
        }
        
        return summary

    def calculate_full_scores(self, df_num: int, user_inputs: List[float], baseline_inputs: List[float]) -> Dict[str, Dict[str, float]]:
        """
        Calcule score, baseline et relative importance pour chaque objectif d'un DF.
        """
        df_map = self.get_df_map(df_num)
        results = {}
        for objective, data in df_map.items():
            weights = data['weights']
            if len(weights) == len(user_inputs) == len(baseline_inputs):
                user_score = sum(i * w for i, w in zip(user_inputs, weights))
                baseline_score = sum(b * w for b, w in zip(baseline_inputs, weights))
                ri = ((user_score - baseline_score) / baseline_score * 100) if baseline_score != 0 else 0
                results[objective] = {
                    'score': user_score,
                    'baseline': baseline_score,
                    'relative_importance': ri
                }
        return results

# Valeurs baseline par défaut pour chaque DF (à adapter selon le nombre d'axes de chaque DF)
DF_BASELINES = {
    1: [3, 3, 3, 3],
    2: [3] * 13,  # Adapter selon le nombre d'axes pour DF2
    3: [3] * 19,  # Adapter selon le nombre d'axes pour DF3
    4: [2] * 20,  # Adapter selon le nombre d'axes pour DF4
    5: [1] * 2,   # Adapter selon le nombre d'axes pour DF5
    6: [2] * 3,   # Adapter selon le nombre d'axes pour DF6
    7: [3] * 4,   # Adapter selon le nombre d'axes pour DF7
    8: [1] * 3,   # Adapter selon le nombre d'axes pour DF8
    9: [1] * 3,   # Adapter selon le nombre d'axes pour DF9
    10: [2] * 3,  # Adapter selon le nombre d'axes pour DF10
}

# Déclaration des matrices de pondération et baseline pour DF1 à DF10 (extraits du document COBIT)
DF_MATRICES = {
    1: {
        "matrix": {
            "EDM01": [1.0, 1.0, 1.5, 1.5],
            "EDM02": [1.5, 1.0, 2.0, 3.5],
            "EDM03": [1.0, 1.0, 1.0, 2.0],
            "EDM04": [1.5, 1.0, 4.0, 1.0],
            "EDM05": [1.5, 1.5, 1.0, 2.0],
            "APO01": [1.0, 1.0, 1.0, 1.0],
            "APO02": [3.5, 3.5, 1.5, 1.0],
            "APO03": [4.0, 2.0, 1.0, 1.0],
            "APO04": [1.0, 4.0, 1.0, 1.0],
            "APO05": [3.5, 4.0, 2.5, 1.0],
            "APO06": [1.5, 1.0, 4.0, 1.0],
            "APO07": [2.0, 1.0, 1.0, 1.0],
            "APO08": [1.0, 1.5, 1.0, 3.5],
            "APO09": [1.0, 1.0, 1.5, 4.0],
            "APO10": [1.0, 1.0, 3.5, 1.5],
            "APO11": [1.0, 1.0, 1.0, 4.0],
            "APO12": [1.0, 1.5, 1.0, 2.5],
            "APO13": [1.0, 1.0, 1.0, 2.5],
            "APO14": [1.0, 1.0, 1.0, 1.0],
            "BAI01": [4.0, 2.0, 1.5, 1.5],
            "BAI02": [1.0, 1.0, 1.5, 1.0],
            "BAI03": [1.0, 1.0, 1.5, 1.0],
            "BAI04": [1.0, 1.0, 1.0, 3.0],
            "BAI05": [4.0, 2.0, 1.0, 1.5],
            "BAI06": [2.0, 2.0, 1.0, 1.5],
            "BAI07": [1.5, 2.0, 1.0, 1.5],
            "BAI08": [1.0, 3.5, 1.0, 1.0],
            "BAI09": [1.0, 1.0, 1.0, 1.0],
            "BAI10": [1.0, 1.0, 1.0, 1.0],
            "BAI11": [3.5, 3.0, 1.5, 1.0],
            "DSS01": [1.0, 1.0, 1.0, 1.5],
            "DSS02": [1.0, 1.0, 1.0, 4.0],
            "DSS03": [1.0, 1.0, 1.0, 3.0],
            "DSS04": [1.0, 1.0, 1.0, 4.0],
            "DSS05": [1.0, 1.0, 1.0, 2.5],
            "DSS06": [1.0, 1.0, 1.0, 1.5],
            "MEA01": [1.0, 1.0, 1.0, 1.0],
            "MEA02": [1.0, 1.0, 1.0, 1.0],
            "MEA03": [1.0, 1.0, 1.0, 1.0],
            "MEA04": [1.0, 1.0, 1.0, 1.0],
        },
        "baseline": [3, 3, 3, 3],
        "axes": ["Growth/Acquisition", "Innovation/Differentiation", "Cost Leadership", "Client Service/Stability"]
    },
    2: {
        "matrix": {
            # Remplir avec la matrice DF2 (voir document, 13 axes)
            # Exemple fictif (à remplacer par la vraie matrice) :
            "EDM01": [1,1,1,1,1,1,1,1,1,1,1,1,1],
            # ...
        },
        "baseline": [3]*13,
        "axes": [f"EG{i:02d}" for i in range(1,14)]
    },
    3: {
        "matrix": {
            # Remplir avec la matrice DF3 (19 axes)
            "EDM01": [1]*19,
            # ...
        },
        "baseline": [3]*19,
        "axes": [f"RISKCAT{i:02d}" for i in range(1,20)]
    },
    4: {
        "matrix": {
            # Remplir avec la matrice DF4 (20 axes)
            "EDM01": [1]*20,
            # ...
        },
        "baseline": [2]*20,
        "axes": [f"ISSUE{i:02d}" for i in range(1,21)]
    },
    5: {
        "matrix": {
            # Remplir avec la matrice DF5 (2 axes)
            "EDM01": [1,1],
            # ...
        },
        "baseline": [1,1],
        "axes": ["High", "Normal"]
    },
    6: {
        "matrix": {
            # Remplir avec la matrice DF6 (3 axes)
            "EDM01": [1,1,1],
            # ...
        },
        "baseline": [2,2,2],
        "axes": ["High", "Normal", "Low"]
    },
    7: {
        "matrix": {
            # Remplir avec la matrice DF7 (4 axes)
            "EDM01": [1,1,1,1],
            # ...
        },
        "baseline": [3,3,3,3],
        "axes": ["Support", "Factory", "Turnaround", "Strategic"]
    },
    8: {
        "matrix": {
            # Remplir avec la matrice DF8 (3 axes)
            "EDM01": [1,1,1],
            # ...
        },
        "baseline": [1,1,1],
        "axes": ["Outsourcing", "Cloud", "Insourcing"]
    },
    9: {
        "matrix": {
            # Remplir avec la matrice DF9 (3 axes)
            "EDM01": [1,1,1],
            # ...
        },
        "baseline": [1,1,1],
        "axes": ["Agile", "DevOps", "Traditional"]
    },
    10: {
        "matrix": {
            # Remplir avec la matrice DF10 (3 axes)
            "EDM01": [1,1,1],
            # ...
        },
        "baseline": [2,2,2],
        "axes": ["First Mover", "Follower", "Slow Adopter"]
    },
}

def calculate_df_scores_no_file(df_num: int, user_inputs: list) -> dict:
    df_data = DF_MATRICES[df_num]
    matrix = df_data["matrix"]
    baseline = df_data["baseline"]
    results = {}
    for obj, weights in matrix.items():
        user_score = sum(i * w for i, w in zip(user_inputs, weights))
        baseline_score = sum(b * w for b, w in zip(baseline, weights))
        ri = ((user_score - baseline_score) / baseline_score * 100) if baseline_score != 0 else 0
        results[obj] = {
            "score": user_score,
            "baseline": baseline_score,
            "relative_importance": ri
        }
    return results

# Instance globale du parser
cobit_parser = COBITParser()

def get_cobit_data():
    """Fonction utilitaire pour accéder aux données COBIT."""
    return cobit_parser.get_all_df_data()

def get_df_data(df_num: int):
    """Fonction utilitaire pour obtenir les données d'un DF."""
    return cobit_parser.get_df_summary(df_num) 

def calculate_df_scores(df_num: int, user_inputs: List[float]) -> Dict[str, Dict[str, float]]:
    """
    Calcule score, baseline et RI pour chaque objectif d'un DF à partir des inputs utilisateur.
    """
    baseline_inputs = DF_BASELINES.get(df_num, [3] * len(user_inputs))
    return cobit_parser.calculate_full_scores(df_num, user_inputs, baseline_inputs)

# Exemple de test pour DF1 et DF2
if __name__ == "__main__":
    print("Test DF1 avec inputs [2, 5, 1, 4] :")
    results1 = calculate_df_scores_no_file(1, [2, 5, 1, 4])
    for obj, vals in results1.items():
        print(f"{obj}: score={vals['score']:.2f}, baseline={vals['baseline']:.2f}, RI={vals['relative_importance']:.2f}%")
    print("\nTest DF2 avec inputs [3]*13 :")
    results2 = calculate_df_scores_no_file(2, [3]*13)
    for obj, vals in results2.items():
        print(f"{obj}: score={vals['score']:.2f}, baseline={vals['baseline']:.2f}, RI={vals['relative_importance']:.2f}%") 