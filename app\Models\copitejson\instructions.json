{"data": [{"record": {"@index": "1", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "2", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Terms & Definitions", "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "3", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "4", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "Relative importance", "Unnamed:_2": "Relative importance (of governance and management objectives) is a number that indicates the influence of a certain design factor on the importance of a certain COBIT governance or management objective as compared to a baseline (standard) situation. The number is calculated as a percentage difference between the baseline and the current situation, as determined by the values given to the design factor at hand.", "Unnamed:_3": {}}}, {"record": {"@index": "5", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "6", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Instructions", "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "7", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "8", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Sheet", "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "9", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "<PERSON><PERSON>", "Unnamed:_1": {}, "Unnamed:_2": "In this sheet all results of the impact assessment of the design factors are summarized. This is done in line with the governance system design flow explained in the COBIT Design Guide.\n  \n  The user can provide input in columns R/S to adjust the results of the automated calculations, taking into account the enterprise's specific context.  When making adjustments in column R, the spreadsheet expects an explanation in column S.", "Unnamed:_3": {}}}, {"record": {"@index": "10", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Sheet", "Unnamed:_1": {}, "Unnamed:_2": "Input Section", "Unnamed:_3": "Output Section"}}, {"record": {"@index": "11", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF1", "Unnamed:_1": "Description", "Unnamed:_2": "In this sheet, the importance of different enterprise strategies can be described. The importance is expressed as an integer value between 1 (Not Important) and 5 (Critical) and can be entered in cells C8-C11.  \n  \n  The chosen values are represented graphically in the two diagrams in the input section. The diagrams depict the same information, one in a bar chart, the other in a spider chart.", "Unnamed:_3": "The output section of this sheet contains the calculated relative importance of each of the 40 COBIT 2019 Governance and Management Objectives"}}, {"record": {"@index": "12", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": "[Optional] Enter values between 1 and 5 expressing the importance or relevance of each of the given generic enterprise strategies for the user enterprise", "Unnamed:_3": "a) Observe the resulting importance scores for each of the 40 governance/management objectives.\n  b) [Optional] Use the graphic(s) for reporting the outcome of this step in the governance system design process. Both diagrams contain the same information but in a different representation. Use the one that suits you best."}}, {"record": {"@index": "13", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF2", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "14", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "15", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF3", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "16", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "17", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF4", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "18", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "19", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF5", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "20", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "21", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF6", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "22", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "23", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF7", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "24", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "25", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF8", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "26", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "27", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF9", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "28", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "29", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "DF10", "Unnamed:_1": "Description", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "30", "COBIT__2019_Governance_System_Design_Workbook_Instructions": {}, "Unnamed:_1": "User Action Required", "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "31", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Chart 1", "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}, {"record": {"@index": "32", "COBIT__2019_Governance_System_Design_Workbook_Instructions": "Chart 2", "Unnamed:_1": {}, "Unnamed:_2": {}, "Unnamed:_3": {}}}]}