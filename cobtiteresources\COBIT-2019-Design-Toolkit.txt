COBIT® 2019 Governance System Design Workbook—Instructions			

Terms & Definitions			

	Relative importance	Relative importance (of governance and management objectives) is a number that indicates the influence of a certain design factor on the importance of a certain COBIT governance or management objective as compared to a baseline (standard) situation. The number is calculated as a percentage difference between the baseline and the current situation, as determined by the values given to the design factor at hand.	

Instructions			

Sheet			
Canvas		"In this sheet all results of the impact assessment of the design factors are summarized. This is done in line with the governance system design flow explained in the COBIT Design Guide.

The user can provide input in columns R/S to adjust the results of the automated calculations, taking into account the enterprise's specific context.  When making adjustments in column R, the spreadsheet expects an explanation in column S. "	
Sheet		Input Section	Output Section
DF1	Description	"In this sheet, the importance of different enterprise strategies can be described. The importance is expressed as an integer value between 1 (Not Important) and 5 (Critical) and can be entered in cells C8-C11.  

The chosen values are represented graphically in the two diagrams in the input section. The diagrams depict the same information, one in a bar chart, the other in a spider chart."	The output section of this sheet contains the calculated relative importance of each of the 40 COBIT 2019 Governance and Management Objectives
	User Action Required	[Optional] Enter values between 1 and 5 expressing the importance or relevance of each of the given generic enterprise strategies for the user enterprise	"a) Observe the resulting importance scores for each of the 40 governance/management objectives.
b) [Optional] Use the graphic(s) for reporting the outcome of this step in the governance system design process. Both diagrams contain the same information but in a different representation. Use the one that suits you best."
DF2	Description		
	User Action Required		
DF3	Description		
	User Action Required		
DF4	Description		
	User Action Required		
DF5	Description		
	User Action Required		
DF6	Description		
	User Action Required		
DF7	Description		
	User Action Required		
DF8	Description		
	User Action Required		
DF9	Description		
	User Action Required		
DF10	Description		
	User Action Required		
Chart 1			
Chart 2			
